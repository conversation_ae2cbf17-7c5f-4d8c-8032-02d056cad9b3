"""
产品服务模块
封装产品详情获取逻辑，支持缓存
"""
import httpx
from typing import Dict, Any, Optional
import asyncio
from ..crud import get_product_info, upsert_product_info, add_basket_sample, get_basket_by_short_id
from ..utils import normalize_product_id, retry_async, timing_decorator
from ..logging import log_success, log_error, log_warning, log_debug

def _parse_product_url(product_id: int, product_img_num: int = 1) -> Dict[str, Any]:
    """
    解析产品URL
    
    Args:
        product_id: 产品ID
        product_img_num: 图片数量
        
    Returns:
        包含产品URL和图片URL的字典
    """
    short_id = product_id // 100000

    basket = ''
    if short_id <= 3486:
        if 0 <= short_id <= 143:
            basket = '01'
        elif 144 <= short_id <= 287:
            basket = '02'
        elif 288 <= short_id <= 431:
            basket = '03'
        elif 432 <= short_id <= 719:
            basket = '04'
        elif 720 <= short_id <= 1007:
            basket = '05'
        elif 1008 <= short_id <= 1061:
            basket = '06'
        elif 1062 <= short_id <= 1115:
            basket = '07'
        elif 1116 <= short_id <= 1169:
            basket = '08'
        elif 1170 <= short_id <= 1313:
            basket = '09'
        elif 1314 <= short_id <= 1601:
            basket = '10'
        elif 1602 <= short_id <= 1655:
            basket = '11'
        elif 1656 <= short_id <= 1919:
            basket = '12'
        elif 1920 <= short_id <= 2045:
            basket = '13'
        elif 2046 <= short_id <= 2189:
            basket = '14'
        elif 2190 <= short_id <= 2405:
            basket = '15'
        elif 2406 <= short_id <= 2621:
            basket = '16'
        elif 2622 <= short_id <= 2837:
            basket = '17'
        elif 2838 <= short_id <= 3053:
            basket = '18'
        elif 3054 <= short_id <= 3270:
            basket = '19'
        elif 3271 <= short_id <= 3486:
            basket = '20'
    else:
        delta = short_id - 3487
        basket_num = 21 + (delta // 216)
        basket = str(basket_num).zfill(2)

    part = product_id // 1000
    product_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"

    parse_product_urls = [
        f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/images/big/{i}.webp"
        for i in range(1, int(product_img_num) + 1)
    ]

    return {
        "parse_product_urls": parse_product_urls,
        "product_url": product_url,
        "basket": basket
    }

async def _detect_correct_basket(product_id: int) -> Optional[str]:
    """
    动态探测正确的basket编号
    在估算值附近±3范围内探测

    Args:
        product_id: 产品ID

    Returns:
        正确的basket编号，或None
    """
    short_id = product_id // 100000
    part = product_id // 1000

    # 获取估算的basket
    estimated_data = _parse_product_url(product_id)
    estimated_basket = estimated_data["basket"]

    # 在估算值附近探测
    try:
        estimated_num = int(estimated_basket)
        test_range = range(max(1, estimated_num - 3), estimated_num + 4)
    except ValueError:
        # 如果估算basket不是数字，使用默认范围
        test_range = range(1, 50)

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Referer": f"https://www.wildberries.ru/catalog/{product_id}/detail.aspx",
        "Origin": "https://www.wildberries.ru",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }

    async with httpx.AsyncClient(timeout=10) as client:
        for basket_num in test_range:
            basket = str(basket_num).zfill(2)
            test_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"

            try:
                # 使用HEAD请求减少网络开销
                response = await client.head(test_url, headers=headers)
                if response.status_code == 200:
                    log_debug(f"动态探测成功: product_id={product_id}, basket={basket}")

                    # 记录成功的样本
                    await add_basket_sample(basket, short_id)

                    return basket

            except Exception as e:
                log_debug(f"探测basket失败: basket={basket}, error={e}")
                continue

    log_warning(f"动态探测失败，未找到有效basket: product_id={product_id}")
    return None

async def _parse_product_url_smart(product_id: int, product_img_num: int = 1) -> Dict[str, Any]:
    """
    智能解析产品URL（优先数据库，传统算法兜底）

    Args:
        product_id: 产品ID
        product_img_num: 图片数量

    Returns:
        包含产品URL、图片URL、basket和确定性信息的字典
    """
    short_id = product_id // 100000
    part = product_id // 1000

    # 1. 首先查数据库：看short_id是否在已知范围内
    basket_info = await get_basket_by_short_id(short_id)

    if basket_info and basket_info["in_range"]:
        # 数据库中有确定的范围，直接使用
        basket = basket_info["basket"]
        is_certain = True
        source = "database_range"
        log_debug(f"数据库范围命中: product_id={product_id}, basket={basket}")
    else:
        # 2. 数据库中没有范围，使用传统算法计算
        traditional_data = _parse_product_url(product_id, product_img_num)
        basket = traditional_data["basket"]
        is_certain = False
        source = "traditional_algorithm"
        log_debug(f"使用传统算法: product_id={product_id}, basket={basket}")

    # 构建URL
    product_url = f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/info/ru/card.json"

    parse_product_urls = [
        f"https://basket-{basket}.wbbasket.ru/vol{short_id}/part{part}/{product_id}/images/big/{i}.webp"
        for i in range(1, int(product_img_num) + 1)
    ]

    return {
        "parse_product_urls": parse_product_urls,
        "product_url": product_url,
        "basket": basket,
        "is_certain": is_certain,
        "source": source
    }

@retry_async(max_retries=3, delay=1.0, backoff=2.0)
@timing_decorator
async def _fetch_from_remote(nm_id: int) -> Dict[str, Any]:
    """
    从远程API获取产品信息（智能basket处理）

    Args:
        nm_id: 产品ID

    Returns:
        产品信息字典

    Raises:
        httpx.HTTPError: 当HTTP请求失败时
        ValueError: 当产品信息无效时
    """
    # 使用智能URL解析
    product_data = await _parse_product_url_smart(nm_id, product_img_num=5)
    url = product_data['product_url']
    img_urls = product_data['parse_product_urls']
    basket = product_data['basket']
    is_certain = product_data['is_certain']
    source = product_data['source']

    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
        "Accept": "application/json, text/plain, */*",
        "Referer": f"https://www.wildberries.ru/catalog/{nm_id}/detail.aspx",
        "Origin": "https://www.wildberries.ru",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Accept-Language": "zh-CN,zh;q=0.9"
    }

    async with httpx.AsyncClient(timeout=20) as client:
        try:
            response = await client.get(url, headers=headers)
            response.raise_for_status()
            product_info = response.json()

            if not product_info:
                raise ValueError(f"产品信息为空: {nm_id}")

            # 添加图片URL
            product_info["product_img_urls"] = img_urls

            # 清洗无效数据，格式化product_info，压缩最小字符
            keys_to_remove = ['colors', 'full_colors', 'selling', 'media', 'data', 'slug', 'certificate']
            for key in keys_to_remove:
                product_info.pop(key, None)

            # 处理 options 字段为扁平结构
            options = {}
            for option in product_info.get('options', []):
                if 'name' in option and 'value' in option:
                    options[option['name']] = option['value']
            product_info['options'] = options

            # 处理 grouped_options 字段为扁平结构
            grouped_options_child = {}
            for group in product_info.get('grouped_options', []):
                for option in group.get('options', []):
                    if 'name' in option and 'value' in option:
                        grouped_options_child[option['name']] = option['value']
            product_info['grouped_options'] = grouped_options_child

            # 仅保留第一张图片
            product_info['product_img_urls'] = product_info.get('product_img_urls', [])[:1]

            # 3. 请求成功：记录成功样本到数据库，更新范围（无论来源）
            short_id = nm_id // 100000
            await add_basket_sample(basket, short_id)
            log_debug(f"请求成功，记录样本: basket={basket}, short_id={short_id}, source={source}")

            log_success(f"成功获取产品信息: {nm_id}")
            return product_info

        except httpx.HTTPStatusError as e:
            if e.response.status_code == 404:
                # 4. 传统算法失败：启动智能动态调整basket
                log_warning(f"传统算法失败(404)，启动动态探测: basket={basket}, nm_id={nm_id}")

                # 在±3范围内动态探测正确的basket
                correct_basket = await _detect_correct_basket(nm_id)
                if correct_basket:
                    # 使用正确的basket重新构建URL
                    part = nm_id // 1000
                    new_url = f"https://basket-{correct_basket}.wbbasket.ru/vol{short_id}/part{part}/{nm_id}/info/ru/card.json"
                    new_img_urls = [
                        f"https://basket-{correct_basket}.wbbasket.ru/vol{short_id}/part{part}/{nm_id}/images/big/{i}.webp"
                        for i in range(1, 6)
                    ]

                    # 重新请求
                    log_debug(f"使用探测到的basket重新请求: {nm_id}, basket={correct_basket}")
                    response = await client.get(new_url, headers=headers)
                    response.raise_for_status()
                    product_info = response.json()

                    if not product_info:
                        raise ValueError(f"产品信息为空: {nm_id}")

                    # 使用新的图片URL
                    product_info["product_img_urls"] = new_img_urls

                    # 数据清洗（同上）
                    keys_to_remove = ['colors', 'full_colors', 'selling', 'media', 'data', 'slug', 'certificate']
                    for key in keys_to_remove:
                        product_info.pop(key, None)

                    options = {}
                    for option in product_info.get('options', []):
                        if 'name' in option and 'value' in option:
                            options[option['name']] = option['value']
                    product_info['options'] = options

                    grouped_options_child = {}
                    for group in product_info.get('grouped_options', []):
                        for option in group.get('options', []):
                            if 'name' in option and 'value' in option:
                                grouped_options_child[option['name']] = option['value']
                    product_info['grouped_options'] = grouped_options_child

                    product_info['product_img_urls'] = product_info.get('product_img_urls', [])[:1]

                    # 记录动态探测成功的basket样本
                    short_id = nm_id // 100000
                    await add_basket_sample(correct_basket, short_id)
                    log_debug(f"记录动态探测成功样本: basket={correct_basket}, short_id={short_id}")

                    log_success(f"动态探测后成功获取产品信息: {nm_id}")
                    return product_info
                else:
                    log_error(f"动态探测失败，无法获取产品信息: {nm_id}")
                    raise
            else:
                log_error(f"HTTP请求失败: {nm_id}, 状态码: {e.response.status_code}")
                raise
        except httpx.TimeoutException as e:
            log_error(f"请求超时: {nm_id}")
            raise
        except Exception as e:
            log_error(f"获取产品信息失败: {nm_id}", error=e)
            raise

async def get_product_detail(nm_id: int, *, force_refresh: bool = False) -> Dict[str, Any]:
    """
    获取产品详情（带缓存）
    
    Args:
        nm_id: 产品ID
        force_refresh: 是否强制刷新缓存
        
    Returns:
        产品信息字典
        
    Raises:
        ValueError: 当产品ID无效时
        Exception: 当获取产品信息失败时
    """
    nm_id = normalize_product_id(nm_id)
    
    # 如果不强制刷新，先尝试从缓存获取
    if not force_refresh:
        cached = await get_product_info(nm_id)
        if cached:
            log_debug(f"从缓存获取产品信息: {nm_id}")
            return cached

    # 从远程获取
    try:
        info = await _fetch_from_remote(nm_id)
        
        # 保存到缓存
        success = await upsert_product_info(nm_id, info)
        if success:
            log_debug(f"产品信息已缓存: {nm_id}")
        else:
            log_warning(f"产品信息缓存失败: {nm_id}")
        
        return info
        
    except Exception as e:
        log_error(f"获取产品详情失败: {nm_id}", error=e)
        raise

async def get_products_batch(nm_ids: list[int], *, force_refresh: bool = False) -> Dict[int, Dict[str, Any]]:
    """
    批量获取产品详情
    
    Args:
        nm_ids: 产品ID列表
        force_refresh: 是否强制刷新缓存
        
    Returns:
        产品ID到产品信息的映射字典
    """
    if not nm_ids:
        return {}
    
    # 标准化产品ID
    nm_ids = [normalize_product_id(nm_id) for nm_id in nm_ids]
    
    # 并发获取产品信息
    tasks = []
    for nm_id in nm_ids:
        task = asyncio.create_task(
            get_product_detail(nm_id, force_refresh=force_refresh)
        )
        tasks.append((nm_id, task))
    
    results = {}
    for nm_id, task in tasks:
        try:
            product_info = await task
            results[nm_id] = product_info
        except Exception as e:
            log_error(f"批量获取产品信息失败: {nm_id}", error=e)
            # 继续处理其他产品，不中断整个批量操作
    
    log_success(f"批量获取产品信息完成: {len(results)}/{len(nm_ids)}")
    return results

async def refresh_product_cache(nm_id: int) -> bool:
    """
    刷新产品缓存
    
    Args:
        nm_id: 产品ID
        
    Returns:
        刷新是否成功
    """
    try:
        await get_product_detail(nm_id, force_refresh=True)
        log_success(f"产品缓存刷新成功: {nm_id}")
        return True
    except Exception as e:
        log_error(f"产品缓存刷新失败: {nm_id}", error=e)
        return False

async def validate_product_exists(nm_id: int) -> bool:
    """
    验证产品是否存在
    
    Args:
        nm_id: 产品ID
        
    Returns:
        产品是否存在
    """
    try:
        await get_product_detail(nm_id)
        return True
    except Exception:
        return False
