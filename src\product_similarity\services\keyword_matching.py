"""
关键词匹配分析服务
分析关键词与产品的关联度
"""
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from ..logging import log_success, log_error, log_warning, log_debug
from ..crud import with_connection
from ..utils import normalize_product_id
from .wildberries_search import search_wildberries, extract_product_ids_from_search
from .similarity import compare_products_by_ids
import asyncpg


@with_connection
async def get_keyword_analysis_result(
    conn: asyncpg.Connection, 
    keyword: str, 
    target_product_id: int
) -> Optional[asyncpg.Record]:
    """获取关键词分析结果
    
    参数:
        conn: 数据库连接
        keyword: 关键词
        target_product_id: 目标产品ID
        
    返回:
        分析结果记录或None
    """
    try:
        target_product_id = normalize_product_id(target_product_id)
        
        row = await conn.fetchrow(
            """
            SELECT * FROM pj_similar.product_analyze_similar_result
            WHERE keyword = $1 AND target_product_id = $2
            """,
            keyword, target_product_id
        )
        
        if row:
            log_debug(f"获取关键词分析结果成功: {keyword} -> {target_product_id}")
        
        return row
        
    except Exception as e:
        log_error(f"获取关键词分析结果失败: {keyword} -> {target_product_id}", error=e)
        return None


@with_connection
async def insert_keyword_analysis_result(
    conn: asyncpg.Connection,
    keyword: str,
    target_product_id: int,
    avg_similarity: float,
    similar_count: int,
    competitor_count: int,
    valid_scores: int
) -> bool:
    """插入关键词分析结果

    参数:
        conn: 数据库连接
        keyword: 关键词
        target_product_id: 目标产品ID
        avg_similarity: 平均相似度
        similar_count: 相似数量（>65分）
        competitor_count: 竞品数量（>80分）
        valid_scores: 有效样本数量

    返回:
        操作是否成功
    """
    try:
        target_product_id = normalize_product_id(target_product_id)
        # 将平均相似度转换为整型
        avg_similarity_int = int(round(avg_similarity))
        
        await conn.execute(
            """
            INSERT INTO pj_similar.product_analyze_similar_result
            (keyword, target_product_id, avg_similarity, similar_count, competitor_count, valid_scores, created_at)
            VALUES ($1, $2, $3, $4, $5, $6, NOW())
            ON CONFLICT (keyword, target_product_id)
            DO UPDATE SET
                avg_similarity = EXCLUDED.avg_similarity,
                similar_count = EXCLUDED.similar_count,
                competitor_count = EXCLUDED.competitor_count,
                valid_scores = EXCLUDED.valid_scores,
                created_at = NOW()
            """,
            keyword, target_product_id, avg_similarity_int, similar_count,
            competitor_count, valid_scores
        )
        
        log_success(f"关键词分析结果已保存: {keyword} -> {target_product_id}")
        return True
        
    except Exception as e:
        log_error(f"保存关键词分析结果失败: {keyword} -> {target_product_id}", error=e)
        return False


async def analyze_similarity_scores(similarity_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析相似度分数统计
    
    参数:
        similarity_results: 相似度比较结果列表
        
    返回:
        统计结果字典
    """
    valid_scores = []
    similar_count = 0  # >65分
    competitor_count = 0  # >80分
    
    for result in similarity_results:
        if result and 'similar_scores' in result:
            score = result['similar_scores']
            if isinstance(score, (int, float)) and 1 <= score <= 100:
                valid_scores.append(score)
                
                if score > 65:
                    similar_count += 1
                if score > 80:
                    competitor_count += 1
    
    # 计算平均分
    avg_similarity = sum(valid_scores) / len(valid_scores) if valid_scores else 0
    
    return {
        'avg_similarity': avg_similarity,
        'similar_count': similar_count,
        'competitor_count': competitor_count,
        'valid_scores': len(valid_scores)
    }


async def keywordMatching(keyword: str, target_product_id: int) -> Dict[str, Any]:
    """关键词匹配分析函数
    
    分析关键词与产品的关联度
    
    参数:
        keyword: 关键词
        target_product_id: 目标产品ID
        
    返回:
        分析结果字典
    """
    try:
        target_product_id = normalize_product_id(target_product_id)
        
        # 1. 先检查是否已存在结果
        existing_result = await get_keyword_analysis_result(keyword, target_product_id)
        if existing_result:
            log_debug(f"返回已存在的关键词分析结果: {keyword} -> {target_product_id}")
            return {
                'status': 'success',
                'data': {
                    'keyword': keyword,
                    'target_product_id': target_product_id,
                    'avg_similarity': existing_result['avg_similarity'],
                    'similar_count': existing_result['similar_count'],
                    'competitor_count': existing_result['competitor_count'],
                    'valid_scores': existing_result['valid_scores'],
                    'created_at': existing_result['created_at'],
                    'from_cache': True
                }
            }
        
        # 2. 通过关键词搜索获取前100名产品列表
        log_debug(f"开始搜索关键词: {keyword}")
        search_result = await search_wildberries(keyword)
        if not search_result:
            return {
                'status': 'error',
                'message': f'搜索关键词失败: {keyword}'
            }
        
        # 3. 提取前50名产品ID
        product_ids = extract_product_ids_from_search(search_result, limit=50)
        if not product_ids:
            return {
                'status': 'error',
                'message': f'未找到相关产品: {keyword}'
            }
        
        log_debug(f"找到 {len(product_ids)} 个产品，开始相似度比较")
        
        # 4. 与目标产品进行相似度比较
        similarity_results = []
        
        # 使用信号量限制并发数
        semaphore = asyncio.Semaphore(5)  # 最多5个并发请求
        
        async def compare_with_semaphore(product_id: int) -> Optional[Dict[str, Any]]:
            async with semaphore:
                try:
                    if product_id == target_product_id:
                        return None  # 跳过自己
                    
                    result = await compare_products_by_ids(
                        target_product_id, 
                        product_id,
                        mode="text"
                    )
                    return result
                except Exception as e:
                    log_warning(f"产品比较失败: {target_product_id} vs {product_id}", error=e)
                    return None
        
        # 并发执行相似度比较
        tasks = [compare_with_semaphore(pid) for pid in product_ids]
        similarity_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 过滤掉异常和None结果
        valid_results = [
            result for result in similarity_results 
            if result is not None and not isinstance(result, Exception)
        ]
        
        log_debug(f"完成 {len(valid_results)} 个有效的相似度比较")
        
        # 5. 分析统计结果
        stats = await analyze_similarity_scores(valid_results)
        
        # 6. 保存结果到数据库
        success = await insert_keyword_analysis_result(
            keyword=keyword,
            target_product_id=target_product_id,
            avg_similarity=stats['avg_similarity'],
            similar_count=stats['similar_count'],
            competitor_count=stats['competitor_count'],
            valid_scores=stats['valid_scores']
        )
        
        if not success:
            log_warning(f"保存关键词分析结果失败: {keyword} -> {target_product_id}")
        
        # 7. 返回结果
        return {
            'status': 'success',
            'data': {
                'keyword': keyword,
                'target_product_id': target_product_id,
                'avg_similarity': int(round(stats['avg_similarity'])),
                'similar_count': stats['similar_count'],
                'competitor_count': stats['competitor_count'],
                'valid_scores': stats['valid_scores'],
                'created_at': datetime.now(),
                'from_cache': False,
                'search_products_count': len(product_ids),
                'compared_products_count': len(valid_results)
            }
        }
        
    except Exception as e:
        log_error(f"关键词匹配分析失败: {keyword} -> {target_product_id}", error=e)
        return {
            'status': 'error',
            'message': f'关键词匹配分析失败: {str(e)}'
        }
