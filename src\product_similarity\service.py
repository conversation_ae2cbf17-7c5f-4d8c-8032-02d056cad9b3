"""
核心业务逻辑层
封装主要的业务功能
"""
from typing import Dict, Any, List, Optional, Tuple
import asyncio

from .services.product import get_product_detail, get_products_batch, validate_product_exists
from .services.similarity import (
    compare_products_by_ids,
    get_product_similarities_list,
    batch_compare_products,
    get_top_similar_products
)
from .services.keyword_matching import keywordMatching
from .crud import get_cache, set_cache, cleanup_expired_cache
from .db import health_check as db_health_check, get_database_stats
from .consul_service import consul_service
from .utils import generate_cache_key, normalize_product_id
from .config import settings
from .logging import log_success, log_error, log_warning, log_info

class ProductSimilarityService:
    """产品相似度服务核心业务类"""
    
    def __init__(self):
        self.service_name = settings.SERVICE_NAME
        self.version = "1.0.0"
    
    # ==================== 产品信息相关 ====================
    
    async def get_product_info(
        self, 
        product_id: int, 
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        获取产品信息
        
        Args:
            product_id: 产品ID
            force_refresh: 是否强制刷新缓存
            
        Returns:
            包含产品信息的响应字典
        """
        try:
            product_id = normalize_product_id(product_id)
            product_info = await get_product_detail(product_id, force_refresh=force_refresh)
            
            return {
                "status": "success",
                "data": product_info,
                "product_id": product_id,
                "cached": not force_refresh
            }
        except Exception as e:
            log_error(f"获取产品信息失败: {product_id}", error=e)
            return {
                "status": "error",
                "message": f"获取产品信息失败: {str(e)}",
                "product_id": product_id
            }
    
    async def get_products_batch_info(
        self, 
        product_ids: List[int], 
        force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        批量获取产品信息
        
        Args:
            product_ids: 产品ID列表
            force_refresh: 是否强制刷新缓存
            
        Returns:
            批量产品信息响应字典
        """
        try:
            if not product_ids:
                return {
                    "status": "error",
                    "message": "产品ID列表不能为空"
                }
            
            if len(product_ids) > 100:
                return {
                    "status": "error",
                    "message": "批量获取产品数量不能超过100个"
                }
            
            products_data = await get_products_batch(product_ids, force_refresh=force_refresh)
            
            return {
                "status": "success",
                "data": products_data,
                "total_requested": len(product_ids),
                "total_found": len(products_data)
            }
        except Exception as e:
            log_error("批量获取产品信息失败", error=e)
            return {
                "status": "error",
                "message": f"批量获取产品信息失败: {str(e)}"
            }
    
    # ==================== 产品相似度比较 ====================
    
    async def compare_products(
        self,
        product_id1: int,
        product_id2: int,
        mode: str = "text",
        convert: bool = False
    ) -> Dict[str, Any]:
        """
        比较两个产品的相似度
        
        Args:
            product_id1: 第一个产品ID
            product_id2: 第二个产品ID
            mode: 比较模式
            convert: 是否转换为markdown格式
            
        Returns:
            相似度比较结果
        """
        try:
            product_id1 = normalize_product_id(product_id1)
            product_id2 = normalize_product_id(product_id2)
            
            if product_id1 == product_id2:
                return {
                    "status": "error",
                    "message": "不能比较相同的产品"
                }
            
            # 验证产品是否存在
            exists_tasks = [
                validate_product_exists(product_id1),
                validate_product_exists(product_id2)
            ]
            exists_results = await asyncio.gather(*exists_tasks, return_exceptions=True)
            
            if not all(exists_results):
                missing_ids = []
                if not exists_results[0]:
                    missing_ids.append(product_id1)
                if not exists_results[1]:
                    missing_ids.append(product_id2)
                
                return {
                    "status": "error",
                    "message": f"产品不存在: {missing_ids}"
                }
            
            # 执行比较
            result = await compare_products_by_ids(
                product_id1, product_id2, 
                mode=mode, convert=convert
            )
            
            return {
                "status": "success",
                "data": result,
                "product_id1": product_id1,
                "product_id2": product_id2
            }
            
        except Exception as e:
            log_error(f"产品比较失败: {product_id1} vs {product_id2}", error=e)
            return {
                "status": "error",
                "message": f"产品比较失败: {str(e)}",
                "product_id1": product_id1,
                "product_id2": product_id2
            }
    
    async def batch_compare_products_service(
        self,
        product_pairs: List[Tuple[int, int]],
        mode: str = "text",
        convert: bool = False,
        max_concurrent: int = 5
    ) -> Dict[str, Any]:
        """
        批量比较产品相似度
        
        Args:
            product_pairs: 产品ID对列表
            mode: 比较模式
            convert: 是否转换为markdown格式
            max_concurrent: 最大并发数
            
        Returns:
            批量比较结果
        """
        try:
            if not product_pairs:
                return {
                    "status": "error",
                    "message": "产品对列表不能为空"
                }
            
            if len(product_pairs) > 50:
                return {
                    "status": "error",
                    "message": "批量比较产品对数量不能超过50个"
                }
            
            results = await batch_compare_products(
                product_pairs, mode=mode, convert=convert, max_concurrent=max_concurrent
            )
            
            success_count = sum(1 for r in results if r.get("success"))
            failed_count = len(results) - success_count
            
            return {
                "status": "success",
                "data": results,
                "total_pairs": len(product_pairs),
                "success_count": success_count,
                "failed_count": failed_count
            }
            
        except Exception as e:
            log_error("批量产品比较失败", error=e)
            return {
                "status": "error",
                "message": f"批量产品比较失败: {str(e)}"
            }
    
    # ==================== 相似度查询 ====================
    
    async def get_product_similarities(
        self, 
        product_id: int, 
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        获取产品的相似度列表
        
        Args:
            product_id: 产品ID
            limit: 返回结果数量限制
            
        Returns:
            相似度列表响应
        """
        try:
            product_id = normalize_product_id(product_id)
            
            if limit > 100:
                limit = 100
            
            similarities = await get_product_similarities_list(product_id, limit)
            
            return {
                "status": "success",
                "data": similarities,
                "product_id": product_id,
                "total_count": len(similarities)
            }
            
        except Exception as e:
            log_error(f"获取产品相似度列表失败: {product_id}", error=e)
            return {
                "status": "error",
                "message": f"获取产品相似度列表失败: {str(e)}",
                "product_id": product_id
            }
    
    async def get_top_similarities(self, limit: int = 20) -> Dict[str, Any]:
        """
        获取相似度最高的产品对
        
        Args:
            limit: 返回结果数量限制
            
        Returns:
            高相似度产品对列表
        """
        try:
            if limit > 100:
                limit = 100
            
            top_similarities = await get_top_similar_products(limit)
            
            return {
                "status": "success",
                "data": top_similarities,
                "total_count": len(top_similarities)
            }
            
        except Exception as e:
            log_error("获取高相似度产品对失败", error=e)
            return {
                "status": "error",
                "message": f"获取高相似度产品对失败: {str(e)}"
            }

    # ==================== 关键词匹配分析 ====================

    async def keyword_matching_analysis(self, keyword: str, target_product_id: int) -> Dict[str, Any]:
        """
        关键词匹配分析

        Args:
            keyword: 搜索关键词
            target_product_id: 目标产品ID

        Returns:
            关键词匹配分析结果
        """
        try:
            result = await keywordMatching(keyword, target_product_id)
            return result

        except Exception as e:
            log_error(f"关键词匹配分析失败: {keyword} -> {target_product_id}", error=e)
            return {
                "status": "error",
                "message": f"关键词匹配分析失败: {str(e)}"
            }

    # ==================== 系统管理 ====================
    
    async def health_check(self) -> Dict[str, Any]:
        """
        系统健康检查
        
        Returns:
            健康检查结果
        """
        checks = {}
        overall_status = "healthy"
        
        try:
            # 检查数据库
            db_health = await db_health_check()
            checks["database"] = db_health["status"]
            if db_health["status"] != "healthy":
                overall_status = "unhealthy"
            
            # 检查Consul
            consul_health = await consul_service.health_check()
            checks["consul"] = consul_health["status"]
            if consul_health["status"] != "healthy":
                overall_status = "degraded"  # Consul不可用不影响核心功能
            
            # 检查缓存（简单测试）
            try:
                test_key = "health_check_test"
                await set_cache(test_key, {"test": True}, ttl=60)
                cache_result = await get_cache(test_key)
                checks["cache"] = "ok" if cache_result else "error"
            except Exception:
                checks["cache"] = "error"
                if overall_status == "healthy":
                    overall_status = "degraded"
            
            # 检查AI服务（简单检查配置）
            openai_endpoints = settings.get_openai_endpoints()
            checks["ai_service"] = "ok" if openai_endpoints else "not_configured"
            
        except Exception as e:
            log_error("健康检查异常", error=e)
            overall_status = "error"
            checks["system"] = "error"
        
        return {
            "status": overall_status,
            "service": self.service_name,
            "version": self.version,
            "checks": checks
        }
    
    async def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务信息
        
        Returns:
            服务信息字典
        """
        return {
            "service_name": consul_service.service_name,
            "service_id": consul_service.service_id,
            "address": consul_service.service_address,
            "port": consul_service.service_port,
            "tags": consul_service.service_tags,
            "version": self.version,
            "environment": consul_service.environment
        }
    
    async def get_statistics(self) -> Dict[str, Any]:
        """
        获取服务统计信息
        
        Returns:
            统计信息字典
        """
        try:
            stats = await get_database_stats()
            
            # 清理过期缓存
            cleaned_count = await cleanup_expired_cache()
            if cleaned_count > 0:
                log_info(f"清理过期缓存: {cleaned_count} 条")
            
            return {
                "status": "success",
                "data": stats
            }
            
        except Exception as e:
            log_error("获取统计信息失败", error=e)
            return {
                "status": "error",
                "message": f"获取统计信息失败: {str(e)}"
            }

# 创建全局服务实例
product_similarity_service = ProductSimilarityService()
