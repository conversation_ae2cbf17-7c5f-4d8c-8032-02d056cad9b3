#!/usr/bin/env python3
"""
使用真实产品ID测试重构后的智能Basket系统
"""

import asyncio
import sys
import os
from typing import Dict, Any, List
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from product_similarity.services.product import get_product_detail
from product_similarity.crud import get_basket_stats, cleanup_basket_samples
from product_similarity.logging import log_info, log_error, log_success, log_warning
from test_product_ids import nm_ids
# 转换为整数
product_ids = [int(id_str) for id_str in nm_ids]

async def test_batch_products(batch_size: int = 20, max_batches: int = 5):
    """批量测试产品获取"""
    log_info(f"=== 批量测试产品获取 (批次大小: {batch_size}, 最大批次: {max_batches}) ===")
    
    total_products = len(product_ids)
    log_info(f"总产品数量: {total_products}")
    
    success_count = 0
    error_count = 0
    total_tested = 0
    
    # 分批处理
    for batch_num in range(min(max_batches, (total_products + batch_size - 1) // batch_size)):
        start_idx = batch_num * batch_size
        end_idx = min(start_idx + batch_size, total_products)
        batch_ids = product_ids[start_idx:end_idx]
        
        log_info(f"处理批次 {batch_num + 1}: 产品 {start_idx + 1}-{end_idx}")
        
        batch_start_time = time.time()
        batch_success = 0
        batch_errors = 0
        
        # 并发处理批次内的产品
        tasks = []
        for nm_id in batch_ids:
            tasks.append(test_single_product(nm_id))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for i, result in enumerate(results):
            total_tested += 1
            if isinstance(result, Exception):
                batch_errors += 1
                error_count += 1
                log_error(f"产品 {batch_ids[i]} 测试失败", error=result)
            elif result:
                batch_success += 1
                success_count += 1
            else:
                batch_errors += 1
                error_count += 1
        
        batch_time = time.time() - batch_start_time
        log_info(f"批次 {batch_num + 1} 完成: {batch_success}/{len(batch_ids)} 成功, 耗时 {batch_time:.2f}秒")
        
        # 显示当前统计
        stats = await get_basket_stats()
        sample_count = stats.get('sample_stats', {}).get('total_samples', 0)
        mapping_count = stats.get('mapping_stats', {}).get('total_mappings', 0)
        log_info(f"当前样本数: {sample_count}, 映射数: {mapping_count}")
        
        # 批次间短暂休息
        if batch_num < max_batches - 1:
            await asyncio.sleep(1)
    
    # 最终统计
    log_info(f"=== 批量测试完成 ===")
    log_info(f"总测试数: {total_tested}")
    log_info(f"成功数: {success_count}")
    log_info(f"失败数: {error_count}")
    log_info(f"成功率: {success_count/total_tested*100:.1f}%")
    
    # 显示最终basket统计
    final_stats = await get_basket_stats()
    log_info(f"最终统计: {final_stats}")

async def test_single_product(nm_id: int) -> bool:
    """测试单个产品"""
    try:
        product_info = await get_product_detail(nm_id)
        if product_info:
            return True
        else:
            log_warning(f"产品信息为空: {nm_id}")
            return False
    except Exception as e:
        log_error(f"获取产品信息失败: {nm_id}", error=e)
        return False

async def test_basket_accuracy():
    """测试basket准确性"""
    log_info("=== 测试Basket准确性 ===")
    
    # 获取当前统计
    stats = await get_basket_stats()
    sample_stats = stats.get('sample_stats', {})
    mapping_stats = stats.get('mapping_stats', {})
    
    log_info(f"样本统计: {sample_stats}")
    log_info(f"映射统计: {mapping_stats}")
    
    total_samples = sample_stats.get('total_samples', 0)
    unique_baskets = sample_stats.get('unique_baskets', 0)
    total_mappings = mapping_stats.get('total_mappings', 0)
    
    if total_samples > 0:
        log_info(f"平均每个basket的样本数: {total_samples/unique_baskets:.1f}")
    
    if total_mappings > 0:
        avg_samples = mapping_stats.get('avg_samples_per_basket', 0)
        log_info(f"映射表中平均样本数: {avg_samples}")

async def test_system_performance():
    """测试系统性能"""
    log_info("=== 测试系统性能 ===")
    
    # 选择一些产品进行性能测试
    test_ids = product_ids[:10]
    
    # 第一次请求（冷启动）
    start_time = time.time()
    for nm_id in test_ids:
        try:
            await get_product_detail(nm_id, force_refresh=True)
        except:
            pass
    cold_time = time.time() - start_time
    
    # 第二次请求（缓存命中）
    start_time = time.time()
    for nm_id in test_ids:
        try:
            await get_product_detail(nm_id)
        except:
            pass
    warm_time = time.time() - start_time
    
    log_info(f"冷启动时间: {cold_time:.2f}秒 ({cold_time/len(test_ids):.2f}秒/产品)")
    log_info(f"缓存命中时间: {warm_time:.2f}秒 ({warm_time/len(test_ids):.2f}秒/产品)")
    log_info(f"缓存加速比: {cold_time/warm_time:.1f}x")

async def main():
    """主测试函数"""
    log_info("开始测试重构后的智能Basket系统（使用真实产品ID）")
    
    # 运行性能测试
    await test_system_performance()
    
    # 运行批量测试
    await test_batch_products(batch_size=15, max_batches=3)
    
    # 测试准确性
    await test_basket_accuracy()
    
    log_success("所有测试完成")

if __name__ == "__main__":
    asyncio.run(main())
